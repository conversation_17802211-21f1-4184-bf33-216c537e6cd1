import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';

/// Service for caching notification count for instant UI updates
class NotificationCountCache {
  static const String _unreadCountKey = 'notification_unread_count';
  static const String _lastUpdateKey = 'notification_count_last_update';
  
  /// Get cached unread count
  static Future<int> getCachedUnreadCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(_unreadCountKey) ?? 0;
    } catch (e) {
      Logger.error('NotificationCountCache: Error getting cached count: $e');
      return 0;
    }
  }

  /// Update cached unread count
  static Future<void> updateCachedUnreadCount(int count) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_unreadCountKey, count);
      await prefs.setInt(_lastUpdateKey, DateTime.now().millisecondsSinceEpoch);
      Logger.info('NotificationCountCache: Updated cached count to $count');
    } catch (e) {
      Logger.error('NotificationCountCache: Error updating cached count: $e');
    }
  }

  /// Increment cached unread count
  static Future<void> incrementCachedCount() async {
    try {
      final currentCount = await getCachedUnreadCount();
      await updateCachedUnreadCount(currentCount + 1);
    } catch (e) {
      Logger.error('NotificationCountCache: Error incrementing cached count: $e');
    }
  }

  /// Decrement cached unread count
  static Future<void> decrementCachedCount() async {
    try {
      final currentCount = await getCachedUnreadCount();
      final newCount = (currentCount - 1).clamp(0, double.infinity).toInt();
      await updateCachedUnreadCount(newCount);
    } catch (e) {
      Logger.error('NotificationCountCache: Error decrementing cached count: $e');
    }
  }

  /// Reset cached unread count to zero
  static Future<void> resetCachedCount() async {
    try {
      await updateCachedUnreadCount(0);
    } catch (e) {
      Logger.error('NotificationCountCache: Error resetting cached count: $e');
    }
  }

  /// Get last update timestamp
  static Future<DateTime?> getLastUpdateTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt(_lastUpdateKey);
      return timestamp != null ? DateTime.fromMillisecondsSinceEpoch(timestamp) : null;
    } catch (e) {
      Logger.error('NotificationCountCache: Error getting last update time: $e');
      return null;
    }
  }

  /// Check if cache is stale (older than 5 minutes)
  static Future<bool> isCacheStale() async {
    try {
      final lastUpdate = await getLastUpdateTime();
      if (lastUpdate == null) return true;
      
      final now = DateTime.now();
      final difference = now.difference(lastUpdate);
      return difference.inMinutes > 5;
    } catch (e) {
      Logger.error('NotificationCountCache: Error checking cache staleness: $e');
      return true;
    }
  }

  /// Sync cache with database count
  static Future<void> syncWithDatabase(Future<int> Function() getDatabaseCount) async {
    try {
      final databaseCount = await getDatabaseCount();
      await updateCachedUnreadCount(databaseCount);
      Logger.info('NotificationCountCache: Synced cache with database count: $databaseCount');
    } catch (e) {
      Logger.error('NotificationCountCache: Error syncing with database: $e');
    }
  }

  /// Clear all cached data
  static Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_unreadCountKey);
      await prefs.remove(_lastUpdateKey);
      Logger.info('NotificationCountCache: Cleared all cached data');
    } catch (e) {
      Logger.error('NotificationCountCache: Error clearing cache: $e');
    }
  }
}
