import 'dart:async';
import 'dart:math';
import 'logger.dart';

/// Helper class for implementing retry logic with exponential backoff
class RetryHelper {
  /// Execute a function with retry logic and exponential backoff
  static Future<T> executeWithRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration initialDelay = const Duration(milliseconds: 500),
    double backoffMultiplier = 2.0,
    String? operationName,
  }) async {
    int attempt = 0;
    Duration currentDelay = initialDelay;

    while (attempt < maxRetries) {
      try {
        final result = await operation();
        if (attempt > 0 && operationName != null) {
          Logger.info('RetryHelper: $operationName succeeded on attempt ${attempt + 1}');
        }
        return result;
      } catch (error) {
        attempt++;
        
        if (attempt >= maxRetries) {
          Logger.error('RetryHelper: ${operationName ?? 'Operation'} failed after $maxRetries attempts: $error');
          rethrow;
        }

        Logger.warning('RetryHelper: ${operationName ?? 'Operation'} failed on attempt $attempt, retrying in ${currentDelay.inMilliseconds}ms: $error');
        
        await Future.delayed(currentDelay);
        currentDelay = Duration(
          milliseconds: (currentDelay.inMilliseconds * backoffMultiplier).round(),
        );
      }
    }

    throw StateError('Retry logic should not reach this point');
  }

  /// Execute a function with retry logic for database operations
  static Future<T> executeDbOperationWithRetry<T>(
    Future<T> Function() operation, {
    String? operationName,
  }) async {
    return executeWithRetry(
      operation,
      maxRetries: 3,
      initialDelay: const Duration(milliseconds: 100),
      backoffMultiplier: 1.5,
      operationName: operationName ?? 'Database operation',
    );
  }

  /// Execute a function with retry logic for network operations
  static Future<T> executeNetworkOperationWithRetry<T>(
    Future<T> Function() operation, {
    String? operationName,
  }) async {
    return executeWithRetry(
      operation,
      maxRetries: 2,
      initialDelay: const Duration(milliseconds: 1000),
      backoffMultiplier: 2.0,
      operationName: operationName ?? 'Network operation',
    );
  }

  /// Execute a function with retry logic for notification operations
  static Future<T> executeNotificationOperationWithRetry<T>(
    Future<T> Function() operation, {
    String? operationName,
  }) async {
    return executeWithRetry(
      operation,
      maxRetries: 2,
      initialDelay: const Duration(milliseconds: 200),
      backoffMultiplier: 1.5,
      operationName: operationName ?? 'Notification operation',
    );
  }

  /// Check if an error is retryable
  static bool isRetryableError(dynamic error) {
    if (error is TimeoutException) return true;
    if (error is StateError) return false;
    if (error is ArgumentError) return false;
    
    final errorString = error.toString().toLowerCase();
    if (errorString.contains('database is locked')) return true;
    if (errorString.contains('connection')) return true;
    if (errorString.contains('timeout')) return true;
    if (errorString.contains('network')) return true;
    
    return true; // Default to retryable for unknown errors
  }

  /// Execute with conditional retry based on error type
  static Future<T> executeWithConditionalRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration initialDelay = const Duration(milliseconds: 500),
    String? operationName,
  }) async {
    int attempt = 0;
    Duration currentDelay = initialDelay;

    while (attempt < maxRetries) {
      try {
        return await operation();
      } catch (error) {
        attempt++;
        
        if (!isRetryableError(error) || attempt >= maxRetries) {
          Logger.error('RetryHelper: ${operationName ?? 'Operation'} failed permanently: $error');
          rethrow;
        }

        Logger.warning('RetryHelper: ${operationName ?? 'Operation'} failed on attempt $attempt (retryable), retrying: $error');
        
        await Future.delayed(currentDelay);
        currentDelay = Duration(
          milliseconds: (currentDelay.inMilliseconds * 1.5).round(),
        );
      }
    }

    throw StateError('Conditional retry logic should not reach this point');
  }
}
